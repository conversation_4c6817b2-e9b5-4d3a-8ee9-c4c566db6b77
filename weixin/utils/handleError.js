const isObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}
const isString = obj => {
  return Object.prototype.toString.call(obj) === '[object String]'
}

const handleError = err => {
  var msg = err
  if (isObject(err) && isString(err.message)) {
    msg = err.message
  }

  wx.showToast({
    title: msg,
    icon: 'error', // 图标类型
    duration: 3000, // 显示时长（毫秒，默认 1500ms）
    mask: false // 是否显示透明蒙层，防止触摸穿透
  })
}

export default handleError
