const isObject = obj => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

const setItem = (key, value) => {
  try {
    if (isObject(value)) {
      value = JSON.stringify(value)
      wx.setStorageSync(`${key}-object`, true)
    }

    wx.setStorageSync(key, value)
  } catch (e) {
    console.error(`Failed to set storage for key: "${key}"`, e)
  }
}
const getItem = key => {
  try {
    if (wx.getStorageSync(`${key}-object`)) {
      return JSON.parse(wx.getStorageSync(key) || null)
    }

    return wx.getStorageSync(key)
  } catch (e) {
    console.error(`Failed to get storage for key: "${key}"`, e)
    return null
  }
}

const removeItem = key => {
  try {
    wx.removeStorageSync(key)
  } catch (e) {
    console.error(`Failed to remove storage for key: "${key}"`, e)
  }
}

const clearItems = () => {
  try {
    wx.clearStorageSync()
  } catch (e) {
    console.error('Failed to clear storage.', e)
  }
}

export default {
  setItem,
  getItem,
  removeItem,
  clearItems
}
