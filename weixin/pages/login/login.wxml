<view style="padding: 20px; background-color: #fff; min-height: 100vh; box-sizing: border-box;">

  <!-- Logo -->
  <view style="text-align: center; padding: 40px 0;">
    <image src="{{logoUrl}}" style="width: 100px; height: 100px;"></image>
  </view>

  <!-- Phone Input Group -->
  <view style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px; margin-bottom: 20px;">
    <mp-icon icon="cellphone" color="#888" size="{{20}}"></mp-icon>
    <input type="number" placeholder="请输入手机号码" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;"/>
  </view>

  <!-- Graphical Captcha Group -->
  <view style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding-left: 20px; height: 50px; margin-bottom: 20px;">
    <input type="text" placeholder="请输入图形验证码" style="flex: 1; border: none; background-color: transparent; height: 100%;"/>
    <image src="{{captcha.url}}" style="width: 80px; height: 30px; margin-right: 17px; border-radius: 2px;"></image>
  </view>

  <!-- SMS Verification Code Group -->
  <view style="display: flex; align-items: center; background-color: #f2f2f2; border-radius: 25px; padding: 0 20px; height: 50px; margin-bottom: 40px;">
    <mp-icon icon="lock" color="#888" size="{{20}}"></mp-icon>
    <input type="number" placeholder="请输入验证码" style="flex: 1; border: none; background-color: transparent; margin-left: 10px; height: 100%;"/>
    <view style="width: 1px; height: 20px; background-color: #e0e0e0; margin: 0 8px;"></view>
    <text bindtap="getVerificationCode" style="color: {{countdown > 0 ? '#999' : 'var(--primary-color)'}}; font-size: 14px; position: relative;top:-10px; white-space: nowrap;">
      {{countdown > 0 ? countdown + 's 后重发' : '获取验证码'}}
    </text>
  </view>

  <!-- Login Button -->
  <button style="width: 100%; height: 50px; border-radius: 25px; background-color: var(--primary-color); color: white; border: none; font-size: 16px; display: flex; align-items: center; justify-content: center;" bindtap="login">
    登 录
  </button>

</view>
