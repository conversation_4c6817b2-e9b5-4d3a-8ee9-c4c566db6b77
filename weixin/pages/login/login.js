import store from '../../utils/store.js'
import handleError from '../../utils/handleError.js'
import { setToken } from '../../utils/token.js'
import makeClient from '../../utils/makeClient.js'

const client = makeClient()

Page({
  data: {
    logoUrl: '',
    domainInfo: null,
    countdown: 0,
    phone: '',
    code: '',
    captcha: {
      url: '', // 验证码图片 URL
      token: '' // 验证码 token
    }
  },

  async onLoad() {
    try {
      var domainInfo = store.getItem('domainInfo')
      if (!domainInfo) {
        domainInfo = {
          domainName: '156-dev.olading.com'
        }
      }

      const [err, r] = await client.domainInfo({
        body: {
          domain: domainInfo.domainName
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.setData({ domainInfo: r.data.data })
      store.setItem('domainInfo', r.data.data)
      domainInfo = r.data.data

      if (domainInfo && domainInfo.logoUrl) {
        const logoUrl = this.formatImageURL(domainInfo.logoUrl)
        this.setData({ logoUrl })
      }

      const [err2, r2] = await client.createCaptcha()
      if (err2) {
        handleError(err)
        return
      }

      const captchaToken = r2.data.data
      const baseUrl = `https://${domainInfo.domainName}`
      const captchaUrl = `${baseUrl}/api/public/captcha?token=${encodeURIComponent(
        captchaToken
      )}`

      this.setData({
        'captcha.url': captchaUrl,
        'captcha.token': captchaToken
      })
    } catch (e) {
      console.error(e)
    }
  },
  // 点击图片刷新验证码
  onCaptchaTap() {
    this.createCaptcha()
  },
  // 绑定输入框
  onPhoneInput(e) {
    this.setData({ phone: e.detail.value })
  },
  onCodeInput(e) {
    this.setData({ code: e.detail.value })
  },
  formatImageURL(id) {
    var domainInfo = store.getItem('domainInfo')
    const baseUrl = `https://${domainInfo.domainName}`

    return `${baseUrl}/api/public/previewFile/${id}`
  },

  async login() {
    const { phone, code, captcha } = this.data
    if (!phone || !code) {
      wx.showToast({ title: '请输入手机号和验证码', icon: 'none' })
      return
    }

    wx.showLoading({ title: '登录中...' })

    const [err, res] = await client.request('/api/login', {
      method: 'POST',
      body: {
        phone: phone,
        code: code,
        captchaToken: captcha.token // 将验证码 token 传递给后端
      }
    })

    wx.hideLoading()

    if (err) {
      handleError(err)
      return
    }

    if (res && res.data && res.data.token) {
      setToken(res.data.token)
      wx.reLaunch({ url: '/pages/index/index' })
    } else {
      handleError({
        message: (res && res.data && res.data.message) || '登录失败'
      })
      // 登录失败后，通常需要刷新验证码
      this.createCaptcha()
    }
  }
})
