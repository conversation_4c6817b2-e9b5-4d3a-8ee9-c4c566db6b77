<template>
  <div
    class="navigations"
    style="
      display: flex;
      height: 64px;
      overflow: hidden;
      border-bottom: 1px solid #eee;
      border-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    "
  >
    <div
      style="
        display: flex;
        align-items: center;
        gap: 5px;
        flex: 0 0 150px;
        padding-left: 14px;
      "
    >
      <img :src="logo" style="height: 36px" />
      <span style="font-size: 18px; font-weight: 500">灵工系统</span>
    </div>
    <div
      class="item"
      :class="{ active: activeIndex === index }"
      v-for="(item, index) in navigations"
      :key="index"
      @click="handleNavClick(item, index)"
    >
      {{ item.title }}
    </div>
    <div
      class="user-area"
      style="
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-right: 24px;
      "
    >
      <el-dropdown
        @command="handleCommand"
        trigger="click"
        style="cursor: pointer"
      >
        <div class="user-avatar">
          <el-avatar :size="32" icon="el-icon-user-solid"></el-avatar>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="accountSettings">
            <span>账户管理</span>
            <i
              class="el-icon-arrow-right"
              style="margin-left: 8px; color: #909399"
            ></i>
          </el-dropdown-item>
          <el-dropdown-item divided command="logout">
            <span>退出登录</span>
            <i
              class="el-icon-arrow-right"
              style="margin-left: 8px; color: #909399"
            ></i>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { removeToken } from 'kit/helpers/token'
import operateLaborLogo from './operateLaborLogo.png'
export default {
  props: {
    logoURL: String,
    navigations: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    logo() {
      const src = this.logoURL || operateLaborLogo
      if (src.includes('http')) {
        return src
      }
      return `${window.env?.apiPath}/api/public/previewFile/${src}`
    }
  },
  data() {
    return {
      activeIndex: 0
    }
  },
  watch: {
    navigations: {
      immediate: true,
      handler(newNavs) {
        if (!newNavs || newNavs.length === 0) return

        const storedIndexStr = sessionStorage.getItem('mainNavActiveIndex')
        if (storedIndexStr) {
          const storedIndex = parseInt(storedIndexStr, 10)
          if (storedIndex >= 0 && storedIndex < newNavs.length) {
            this.activeIndex = storedIndex
            this.$emit('change', newNavs[storedIndex])
            return // Priority given to session storage
          }
        }

        // Fallback: if no valid index in session storage, use the route.
        this.updateActiveIndexFromRoute()
      }
    },
    '$route.path'() {
      // When route changes, we need to update the active tab.
      this.updateActiveIndexFromRoute()
    }
  },
  methods: {
    handleNavClick(item, index) {
      this.activeIndex = index
      sessionStorage.setItem('mainNavActiveIndex', index)
      this.$emit('change', item)
    },
    updateActiveIndexFromRoute() {
      if (!this.navigations || this.navigations.length === 0) return

      const currentPath = this.$route.path
      const activeNavIndex = this.navigations.findIndex(
        nav =>
          nav.children &&
          nav.children.some(
            child => {
              if (child.path === '/todo') return false
              // 使用更精确的匹配逻辑：当前路径以菜单路径开头，且下一个字符是 '/' 或者是路径结尾
              if (currentPath === child.path) return true
              return currentPath.startsWith(child.path + '/')
            }
          )
      )

      if (activeNavIndex !== -1 && this.activeIndex !== activeNavIndex) {
        this.activeIndex = activeNavIndex
        // When navigating between pages, we should update the session storage as well.
        sessionStorage.setItem('mainNavActiveIndex', activeNavIndex)
        this.$emit('change', this.navigations[activeNavIndex])
      }
    },
    handleCommand(command) {
      if (command === 'accountSettings') {
        this.$router.push('/accountSettings')
      } else if (command === 'logout') {
        removeToken()
        this.$router.push('/login')
      }
    }
  }
}
</script>

<style scoped>
.item {
  flex: 0 0 100px;
  line-height: 62px;
  padding: 0 10px;
  cursor: pointer;
  text-align: center;
}
.item.active {
  border-bottom: 2px solid var(--o-primary-color);
}
</style>
