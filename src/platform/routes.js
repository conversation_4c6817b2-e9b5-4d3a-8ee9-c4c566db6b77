import Index from 'kit/pages/operateLabor/platform/index.vue'
import Login from 'kit/pages/operateLabor/platform/login.vue'
import LoginWithCaptcha from 'kit/pages/operateLabor/platform/loginWithCaptcha.vue'
import Register from 'kit/pages/operateLabor/platform/register.vue'
import FindPassword from 'kit/pages/operateLabor/platform/findPassword.vue'
import Roles from 'kit/pages/operateLabor/platform/roles.vue'
import RolesNew from 'kit/pages/operateLabor/platform/rolesNew.vue'
import SupplierCustomers from 'kit/pages/operateLabor/platform/supplierCustomers.vue'
import SupplierCustomersNew from 'kit/pages/operateLabor/platform/supplierCustomersNew.vue'
import SupplierCustomersEdit from 'kit/pages/operateLabor/platform/supplierCustomersEdit.vue'
import SupplierCustomerDetail from 'kit/pages/operateLabor/platform/supplierCustomerDetail.vue'
import ServiceContracts from 'kit/pages/operateLabor/platform/serviceContracts.vue'
import Corporations from 'kit/pages/operateLabor/platform/corporations.vue'
import CorporationsNew from 'kit/pages/operateLabor/platform/corporationsNew.vue'
import ServiceContractsNew from 'kit/pages/operateLabor/platform/serviceContractsNew.vue'
import ServiceContractsEdit from 'kit/pages/operateLabor/platform/serviceContractsEdit.vue'
import ServiceContractDetail from 'kit/pages/operateLabor/platform/serviceContractDetail.vue'
import SupplierUsers from 'kit/pages/operateLabor/platform/supplierUsers.vue'
import SupplierUsersNew from 'kit/pages/operateLabor/platform/supplierUsersNew.vue'
import SupplierLabor from 'kit/pages/operateLabor/platform/supplierLabor.vue'
import SupplierLaborNew from 'kit/pages/operateLabor/platform/supplierLaborNew.vue'
import SupplierLaborDetail from 'kit/pages/operateLabor/platform/supplierLaborDetail.vue'
import AccountSettings from 'kit/pages/operateLabor/platform/accountSettings.vue'
import ElectronicContract from 'kit/pages/operateLabor/platform/electronicContract.vue'
import SupplierSettings from 'kit/pages/operateLabor/platform/supplierSettings.vue'
import ContractTemplates from 'kit/pages/operateLabor/platform/contractTemplates.vue'
import ContractTemplatesNew from 'kit/pages/operateLabor/platform/contractTemplatesNew.vue'
import SignaturesNew from 'kit/pages/operateLabor/platform/signaturesNew.vue'
import PersonalIncomeTax from 'kit/pages/operateLabor/platform/personalIncomeTax.vue'
import PersonalIncomeTaxDetail from 'kit/pages/operateLabor/platform/personalIncomeTaxDetail.vue'
import ValueAddedTax from 'kit/pages/operateLabor/platform/valueAddedTax.vue'
import ValueAddedTaxDetail from 'kit/pages/operateLabor/platform/valueAddedTaxDetail.vue'
import PersonnelInfoSubmission from 'kit/pages/operateLabor/platform/personnelInfoSubmission.vue'
import PersonnelIncomeInfoSubmission from 'kit/pages/operateLabor/platform/personnelIncomeInfoSubmission.vue'
import EnterpriseInfoSubmission from 'kit/pages/operateLabor/platform/enterpriseInfoSubmission.vue'
import TaxPaymentVoucher from 'kit/pages/operateLabor/platform/taxPaymentVoucher.vue'
import TaxPaymentVoucherDetail from 'kit/pages/operateLabor/platform/taxPaymentVoucherDetail.vue'
import BillingManage from 'kit/pages/operateLabor/platform/billingManage.vue'
import BillingManageDetail from 'kit/pages/operateLabor/platform/billingManageDetail.vue'
import BillingManageDetailSalary from 'kit/pages/operateLabor/platform/billingManageDetailSalary.vue'
import BillingManageDetailManagementFee from 'kit/pages/operateLabor/platform/billingManageDetailManagementFee.vue'
import BillingManageDetailOtherFee from 'kit/pages/operateLabor/platform/billingManageDetailOtherFee.vue'
import Contracts from 'kit/pages/operateLabor/platform/contracts.vue'
import Payrolls from 'kit/pages/operateLabor/platform/payrolls.vue'
import Payroll from 'kit/pages/operateLabor/platform/payroll.vue'
import PreviousIncomeDeductions from 'kit/pages/operateLabor/platform/previousIncomeDeductions.vue'
import PayrollsNew from 'kit/pages/operateLabor/platform/payrollsNew.vue'
import ProxyBatchs from 'kit/pages/operateLabor/platform/proxyBatchs.vue'
import ProxyBatch from 'kit/pages/operateLabor/platform/proxyBatch.vue'
import ProxyOrders from 'kit/pages/operateLabor/platform/proxyOrders.vue'
import Invoices from 'kit/pages/operateLabor/platform/invoices.vue'
import InvoicesNew from 'kit/pages/operateLabor/platform/invoicesNew.vue'
import Invoice from 'kit/pages/operateLabor/platform/invoice.vue'
import NoPermission from 'kit/pages/operateLabor/platform/noPermission.vue'

const routes = [
  {
    path: '/',
    component: Index,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/accountSettings',
    component: AccountSettings,
    meta: {
      title: '账户管理'
    }
  },
  {
    path: '/loginWithCaptcha',
    component: LoginWithCaptcha,
    meta: {
      title: '验证码登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/register',
    component: Register,
    meta: {
      title: '注册',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/findPassword',
    component: FindPassword,
    meta: {
      title: '找回密码',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/roles',
    component: Roles,
    meta: {
      title: '角色管理',
      noNeedBack: true
    }
  },
  {
    path: '/roles/new',
    component: RolesNew,
    meta: {
      title: '新建角色'
    }
  },
  {
    path: '/roles/:id/edit',
    component: RolesNew,
    meta: {
      title: '编辑角色'
    }
  },
  {
    path: '/supplierCustomers',
    component: SupplierCustomers,
    meta: {
      title: '客户管理',
      noNeedBack: true
    }
  },
  {
    path: '/supplierCustomers/new',
    component: SupplierCustomersNew,
    meta: {
      title: '新增客户'
    }
  },
  {
    path: '/supplierCustomers/:id/edit',
    component: SupplierCustomersEdit,
    meta: {
      title: '编辑客户'
    }
  },
  {
    path: '/supplierCustomers/:id',
    component: SupplierCustomerDetail,
    meta: {
      title: '客户详情'
    }
  },
  {
    path: '/serviceContracts',
    component: ServiceContracts,
    meta: {
      title: '服务合同',
      noNeedBack: true
    }
  },
  {
    path: '/corporations',
    component: Corporations,
    meta: {
      title: '作业主体',
      noNeedBack: true
    }
  },
  {
    path: '/corporations/new',
    component: CorporationsNew,
    meta: {
      title: '新建作业主体'
    }
  },
  {
    path: '/corporations/:id/edit',
    component: CorporationsNew,
    meta: {
      title: '编辑作业主体'
    }
  },
  {
    path: '/serviceContracts/new',
    component: ServiceContractsNew,
    meta: {
      title: '添加合同'
    }
  },
  {
    path: '/serviceContracts/edit/:id',
    component: ServiceContractsEdit,
    meta: {
      title: '编辑合同'
    }
  },
  {
    path: '/serviceContracts/:id',
    component: ServiceContractDetail,
    meta: {
      title: '合同详情'
    }
  },
  {
    path: '/supplierUsers',
    component: SupplierUsers,
    meta: {
      title: '用户管理',
      noNeedBack: true
    }
  },
  {
    path: '/supplierUsers/new',
    component: SupplierUsersNew,
    meta: {
      title: '新建用户'
    }
  },
  {
    path: '/supplierUsers/:id/edit',
    component: SupplierUsersNew,
    meta: {
      title: '编辑用户'
    }
  },
  {
    path: '/supplierLabor',
    component: SupplierLabor,
    meta: {
      title: '人员信息',
      noNeedBack: true
    }
  },
  {
    path: '/supplierLabor/new',
    component: SupplierLaborNew,
    meta: {
      title: '添加人员'
    }
  },
  {
    path: '/supplierLabor/:id',
    component: SupplierLaborDetail,
    meta: {
      title: '人员详情'
    }
  },
  {
    path: '/electronicContract',
    component: ElectronicContract,
    meta: {
      title: '电子合同',
      noNeedBack: true
    }
  },
  {
    path: '/supplierSettings',
    component: SupplierSettings,
    meta: {
      title: '平台信息配置',
      noNeedBack: true
    }
  },
  {
    path: '/contracts',
    component: Contracts,
    meta: {
      title: '电子合同',
      noNeedBack: true
    }
  },
  {
    path: '/contractTemplates',
    component: ContractTemplates,
    meta: {
      title: '合同模板管理',
      noNeedBack: true
    }
  },
  {
    path: '/contractTemplates/new',
    component: ContractTemplatesNew,
    meta: {
      title: '新建合同模板'
    }
  },
  {
    path: '/contractTemplates/:id/edit',
    component: ContractTemplatesNew,
    meta: {
      title: '编辑合同模板'
    }
  },
  {
    path: '/signaturesNew',
    component: SignaturesNew,
    meta: {
      title: '发起签约'
    }
  },
  {
    path: '/personalIncomeTax',
    component: PersonalIncomeTax,
    meta: {
      title: '个税申报',
      noNeedBack: true
    }
  },
  {
    path: '/personalIncomeTax/:id',
    component: PersonalIncomeTaxDetail,
    meta: {
      title: '个税申报详情'
    }
  },
  {
    path: '/valueAddedTax',
    component: ValueAddedTax,
    meta: {
      title: '增值税申报',
      noNeedBack: true
    }
  },
  {
    path: '/valueAddedTax/:id',
    component: ValueAddedTaxDetail,
    meta: {
      title: '增值税申报详情'
    }
  },
  {
    path: '/taxPaymentVoucher',
    component: TaxPaymentVoucher,
    meta: {
      title: '税款缴纳',
      noNeedBack: true
    }
  },
  {
    path: '/taxPaymentVoucher/:id',
    component: TaxPaymentVoucherDetail,
    meta: {
      title: '税款缴纳详情'
    }
  },
  {
    path: '/billingManage',
    component: BillingManage,
    meta: {
      title: '账单管理',
      noNeedBack: true
    }
  },
  {
    path: '/billingManage/:id',
    component: BillingManageDetail,
    meta: {
      title: '查看账单'
    }
  },
  {
    path: '/billingManageDetail/salary',
    component: BillingManageDetailSalary,
    meta: {
      title: '账单明细'
    }
  },
  {
    path: '/billingManageDetail/managementFee',
    component: BillingManageDetailManagementFee,
    meta: {
      title: '账单明细'
    }
  },
  {
    path: '/billingManageDetail/otherFee',
    component: BillingManageDetailOtherFee,
    meta: {
      title: '账单明细'
    }
  },
  {
    path: '/taxPaymentVoucher/:id',
    component: TaxPaymentVoucherDetail,
    meta: {
      title: '发票管理',
      noNeedBack: true
    }
  },
  {
    path: '/payrolls',
    component: Payrolls,
    meta: {
      title: '薪酬计算',
      noNeedBack: true
    }
  },
  {
    path: '/previousIncomeDeductions',
    component: PreviousIncomeDeductions,
    meta: {
      title: '上期收入与减除'
    }
  },
  {
    path: '/payrolls/new',
    component: PayrollsNew,
    meta: {
      title: '创建工资表'
    }
  },
  {
    path: '/payrolls/:id',
    component: Payroll,
    meta: {
      title: '工资条详情'
    }
  },
  {
    path: '/proxyBatchs',
    component: ProxyBatchs,
    meta: {
      title: '代发批次',
      noNeedBack: true
    }
  },
  {
    path: '/proxyBatchs/:id',
    component: ProxyBatch,
    meta: {
      title: '代发批次详情'
    }
  },
  {
    path: '/proxyOrders',
    component: ProxyOrders,
    meta: {
      title: '代发订单',
      noNeedBack: true
    }
  },
  {
    path: '/invoices',
    component: Invoices,
    meta: {
      title: '发票管理',
      noNeedBack: true
    }
  },
  {
    path: '/invoices/new',
    component: InvoicesNew,
    meta: {
      title: '申请开票'
    }
  },
  {
    path: '/invoices/:id',
    component: Invoice,
    meta: {
      title: '发票详情'
    }
  },
  {
    path: '/personnelInfoSubmission',
    component: PersonnelInfoSubmission,
    meta: {
      title: '人员信息报送',
      noNeedBack: true
    }
  },
  {
    path: '/personnelIncomeInfoSubmission',
    component: PersonnelIncomeInfoSubmission,
    meta: {
      title: '人员收入信息报送',
      noNeedBack: true
    }
  },
  {
    path: '/enterpriseInfoSubmission',
    component: EnterpriseInfoSubmission,
    meta: {
      title: '企业信息报送',
      noNeedBack: true
    }
  },
  {
    path: '/noPermission',
    component: NoPermission,
    meta: {
      title: '无访问权限',
      noNeedBack: true,
      noNavigationsAndMenus: true
    }
  }
]

export default routes