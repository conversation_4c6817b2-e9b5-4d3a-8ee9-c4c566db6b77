import { Toast } from 'vant'
import { isObject, isString } from './index'
import i18ns from './i18ns'
import toLoginPage from './toLoginPage'
import { removeToken } from './token'

Toast.setDefaultOptions({ duration: 2000 })

let isRedirectingH5 = false

const handleErrorH5 = err => {
  if (err && err.errorCode === 2) {
    // 防止重复跳转
    if (isRedirectingH5) {
      return
    }

    isRedirectingH5 = true
    Toast.fail(err.message || '您的登录已过期，请重新登录', {
      forbidClick: true
    })
    removeToken()

    // 清理所有域名相关的数据
    try {
      import('../pages/operateLabor/platform/context.js').then(({ clearDomainRelatedData }) => {
        clearDomainRelatedData()
      }).catch(() => {
        localStorage.removeItem('domainInfo')
        localStorage.removeItem('supplierUserProfile')
        localStorage.removeItem('smsCountdownEndTime')
        localStorage.removeItem('__authorities__')
      })
    } catch (error) {
      // 如果动态导入失败，手动清理关键数据
      localStorage.removeItem('domainInfo')
      localStorage.removeItem('supplierUserProfile')
      localStorage.removeItem('smsCountdownEndTime')
      localStorage.removeItem('__authorities__')
    }

    setTimeout(() => {
      toLoginPage()
      // 重置标志
      setTimeout(() => {
        isRedirectingH5 = false
      }, 3000)
    }, 2000)
    return
  }

  //重复请求不报错
  if (err && err.message === 'duplicated request') {
    console.warn(err)
    return
  }

  var msg = err
  if (isObject(err) && isString(err.message)) {
    msg = err.message
  }

  if (i18ns(msg)) {
    msg = i18ns(msg)
  }

  Toast.fail(msg, {
    forbidClick: true
  })
}

export default handleErrorH5
