<template>
  <div class="face-auth-page">
    <div class="header">
      <p class="subtitle">请按照提示完成人脸识别</p>
    </div>

    <div class="face-section">
      <!-- 人脸识别区域 -->
      <div class="face-capture-area" v-if="!videoFile.length">
        <div class="face-frame">
          <div class="face-placeholder">
            <div class="avatar-icon">👤</div>
          </div>
          <div class="scan-border">
            <div class="corner top-left"></div>
            <div class="corner top-right"></div>
            <div class="corner bottom-left"></div>
            <div class="corner bottom-right"></div>
          </div>
          <!-- <div class="countdown" v-if="countdown > 0">{{ countdown }}s</div> -->
        </div>
      </div>
      <div class="video-con" style="position: relative" v-else>
        <img
          class="close-btn"
          @click="handleDelete"
          src="https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/merchant-customs-olading-h5/%E7%BC%96%E7%BB%84%202%402x.png"
        />
        <div
          style="display: flex; justify-content: center; align-items: center"
        >
          <video style="width: 200px; height: 200px" controls="controls">
            <source :src="videoUrl" />
          </video>
        </div>
      </div>

      <!-- 提示文字 -->
      <div class="instructions">
        <h3>请录制一段眨眼视频</h3>
        <div class="steps">
          <div class="step">1. 正脸对框</div>
          <div class="step">2. 点击录制按钮开始眨眼</div>
          <div class="step">3. 录制完成后提交</div>
        </div>
      </div>

      <!-- 录制按钮 -->
      <div class="record-section">
        <Button
          class="record-btn"
          :class="{ recording: isRecording }"
          :disabled="isProcessing || isUploading"
          @click="toggleRecording"
        >
          {{ recordButtonText }}
        </Button>
        <input
          ref="fileInput"
          style="display: none"
          id="INPUT-ID"
          capture="user"
          className="upload-input"
          type="file"
          accept="video/*"
          name="file"
          :disabled="isProcessing || isUploading"
          @click="handleDelete"
          @change="handleUploadVideo"
        />
      </div>

      <!-- 协议勾选 -->
      <div class="agreement-section">
        <label class="agreement-checkbox">
          <input v-model="agreedToFaceAuth" type="checkbox" />
          <span class="checkmark"></span>
          <span class="agreement-text">
            阅读并同意
            <span class="agreement-link" @click="showFaceAuthAgreement"
              >《人脸识别服务规则》</span
            >
          </span>
        </label>
      </div>
    </div>

    <!-- 下一步按钮 -->
    <div class="button-section">
      <Button class="next-btn" :disabled="!canProceed" @click="handleNext">
        下一步
      </Button>
    </div>

    <!-- 协议弹窗 -->
    <div
      v-if="agreementVisible"
      class="agreement-modal"
      @click="closeAgreement"
    >
      <div class="agreement-content" @click.stop>
        <div class="agreement-header">
          <h3>人脸识别服务规则</h3>
          <button class="close-btn" @click="closeAgreement">×</button>
        </div>
        <div class="agreement-body">
          <div class="agreement-text-content">
            <p>为了保障您的账户安全，我们需要进行人脸识别验证。</p>
            <p>在使用人脸识别服务时，我们将：</p>
            <ul>
              <li>收集您的面部特征信息用于身份验证</li>
              <li>严格保护您的生物识别信息安全</li>
              <li>不会将您的面部信息用于其他用途</li>
              <li>您可以随时撤销授权</li>
            </ul>
            <p>请确保在光线充足的环境下进行人脸识别，以获得最佳识别效果。</p>
          </div>
        </div>
        <div class="agreement-footer">
          <Button class="read-btn" @click="confirmFaceAuthRead">已阅读</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Button, Toast } from 'vant'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'TaskFaceAuth',
  components: {
    Button
  },

  data() {
    return {
      isRecording: false,
      isProcessing: false,
      countdown: 0,
      countdownTimer: null,
      agreedToFaceAuth: false,
      agreementVisible: false,
      hasRecorded: false,
      videoFile: [],
      videoUrl: '',
      videoBase64: '',
      exceedSize: false,
      videoFileId: '', // 添加视频文件ID
      isUploading: false // 添加上传状态
    }
  },

  computed: {
    recordButtonText() {
      if (this.isUploading) return '上传中...'
      if (this.isProcessing) return '处理中...'
      if (this.isRecording) return '录制中...'
      if (this.hasRecorded) return '重新录制'
      return '录制本人人脸'
    },

    canProceed() {
      return this.hasRecorded && this.agreedToFaceAuth && this.videoFileId && !this.isUploading
    }
  },

  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },

  methods: {
    toggleRecording() {
      if (this.isRecording) {
        this.stopRecording()
      } else {
        this.startRecording()
      }
    },

    startRecording() {
      this.$refs.fileInput.click()

      this.isRecording = true
      this.countdown = 5

      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          this.stopRecording()
        }
      }, 1000)
    },

    stopRecording() {
      // TODO: 停止录制并处理视频
      console.log('停止录制')

      this.isRecording = false
      this.countdown = 0

      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }

      // 模拟处理过程
      this.isProcessing = true
      setTimeout(() => {
        this.isProcessing = false
        this.hasRecorded = true
        this.$message.success('录制完成')
      }, 2000)
    },

    handleDelete() {
      document.getElementById('INPUT-ID').value = null
      this.videoFile = []
      this.videoUrl = ''
      this.videoFileId = '' // 清除文件ID
      this.hasRecorded = false // 重置录制状态
    },

    async handleUploadVideo(e) {
      if (e.target.files.length == 0) {
        // 解决唤起选择框什么都不选就取消引起的异常
        return
      }
      const file = e.target.files[0]
      console.log('file===', file)
      this.exceedSize = false

      // 文件大小验证
      if (file.size > 50 * 1024 * 1024) {
        e.target.value = ''
        this.videoFile = []
        this.exceedSize = true
        Toast('视频文件大小不能超过50MB')
        return
      }

      // 文件格式验证
      // if (file.type !== 'video/mp4' && file.type !== 'video/mov') {
      //   Toast('视频只支持mp4，mov格式，请重新录制')
      //   e.target.value = ''
      //   this.videoFile = []
      //   return
      // }

      // 优化：通过文件名后缀判断格式（兼容iOS）
      const fileName = file.name.toLowerCase()
      const isMp4 = fileName.endsWith('.mp4')
      const isMov = fileName.endsWith('.mov')
      
      if (!isMp4 && !isMov) {
        Toast('视频只支持mp4，mov格式，请重新录制')
        e.target.value = ''
        this.videoFile = []
        return
      }

      // 开始上传
      this.isUploading = true
      try {
        // 上传文件到服务器
        const formData = new FormData()
        formData.append('file', file)

        const [err, response] = await client.uploadFile({
          body: formData
        })

        if (err) {
          handleError(err)
          return
        }

        // 上传成功，保存文件ID和本地预览URL
        this.videoFileId = response.data.fileId
        const url = this.createObjectURL(file)
        this.videoFile = e.target.files
        this.videoUrl = url
        this.hasRecorded = true // 标记已录制完成

        console.log('视频上传成功，文件ID:', this.videoFileId)
        Toast('视频上传成功')

      } catch (error) {
        Toast('视频上传失败，请重试')
        e.target.value = ''
        this.videoFile = []
      } finally {
        this.isUploading = false
      }
    },

    createObjectURL(object) {
      return window.URL
        ? window.URL.createObjectURL(object)
        : window.webkitURL.createObjectURL(object)
    },

    showFaceAuthAgreement() {
      this.agreementVisible = true
    },

    closeAgreement() {
      this.agreementVisible = false
    },

    confirmFaceAuthRead() {
      this.agreedToFaceAuth = true
      this.closeAgreement()
    },

    convertToBase64(file) {
      const reader = new FileReader()
      reader.onload = e => {
        this.videoBase64 = e.target.result
      }
      reader.readAsDataURL(file)
    },

    async handleNext() {
      if (!this.canProceed) return

      // 检查是否有视频文件ID
      if (!this.videoFileId) {
        Toast('请先上传视频文件')
        return
      }

      const [err, r] = await client.apiFaceAuth({
        body: {
          // name: name,
          // idNo: idNo,
          videoBase64: this.videoFileId // 使用文件ID而不是base64
        }
      })
      if (err) {
        handleError(err)
        return
      }
      if (!r.data.success) {
        Toast.fail(r.data.errorMessage)
        return
      }
      Toast.success('实名认证完成')
      this.$router.push('/laborContract')
    }
  }
}
</script>

<style scoped>
@import './styles.css';

.face-auth-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 20px;
}

.header h2 {
  font-size: 24px;
  color: #333;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.face-section {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.face-capture-area {
  margin-bottom: 30px;
}

.face-frame {
  width: 200px;
  height: 200px;
  position: relative;
  margin: 0 auto;
}

.face-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.avatar-icon {
  font-size: 80px;
  color: rgba(255, 255, 255, 0.8);
}

.scan-border {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #4285f4;
}

.corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
  border-radius: 20px 0 0 0;
}

.corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
  border-radius: 0 20px 0 0;
}

.corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 20px;
}

.corner.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
  border-radius: 0 0 20px 0;
}

.countdown {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  font-weight: bold;
  color: #4285f4;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.instructions {
  text-align: center;
  margin-bottom: 30px;
}

.instructions h3 {
  font-size: 18px;
  color: #333;
  margin: 0 0 15px 0;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step {
  font-size: 14px;
  color: #666;
  text-align: left;
}

.record-section {
  margin-bottom: 30px;
}

.record-btn {
  width: 200px;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.record-btn.recording {
  background: #f44336;
  animation: pulse 1s infinite;
}

.record-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.agreement-section {
  margin-bottom: 20px;
}

.agreement-checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  color: #333;
  font-size: 14px;
}

.agreement-checkbox input {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
}

.agreement-checkbox input:checked + .checkmark {
  background: #4285f4;
  border-color: #4285f4;
}

.agreement-checkbox input:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  top: -2px;
  left: 2px;
}

.agreement-link {
  color: #4285f4;
  text-decoration: underline;
  cursor: pointer;
}

.button-section {
  padding: 20px 0 40px 0;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.next-btn {
  width: 100%;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.next-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.next-btn:not(:disabled):hover {
  background: #3367d6;
  transform: translateY(-1px);
}

.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.agreement-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.agreement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.agreement-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
}

.agreement-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.agreement-text-content {
  line-height: 1.6;
  color: #333;
}

.agreement-text-content p {
  margin-bottom: 15px;
}

.agreement-text-content ul {
  padding-left: 20px;
  margin-bottom: 15px;
}

.agreement-text-content li {
  margin-bottom: 8px;
}

.agreement-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.read-btn {
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 40px;
  font-size: 16px;
  cursor: pointer;
}

.close-btn {
  width: 22px;
  height: 22px;
  position: absolute;
  right: -9px;
  top: -9px;
  z-index: 999;
}
</style>
