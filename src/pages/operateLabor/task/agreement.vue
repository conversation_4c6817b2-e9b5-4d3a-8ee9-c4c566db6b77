<template>
  <div class="agreement-page">
    <!-- 头部导航 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <van-icon name="arrow-left" />
        <span>返回</span>
      </div>
      <div class="title">{{ agreementTitle }}</div>
      <div class="placeholder"></div>
    </div>

    <!-- PDF内容展示区域 -->
    <div class="content-container">
      <iframe
        :src="pdfUrl"
        frameborder="0"
        width="100%"
        height="100%"
        style="border: none;"
      ></iframe>
    </div>

    <!-- 底部确认按钮 -->
    <div class="footer">
      <Button
        type="primary"
        size="large"
        @click="confirmRead"
        block
        class="confirm-btn"
      >
        已阅读并同意
      </Button>
    </div>
  </div>
</template>

<script>
import { Button, Icon, Toast } from 'vant'

export default {
  name: 'Agreement',
  
  components: {
    Button,
    [Icon.name]: Icon
  },

  data() {
    return {
      pdfUrl: '',
      agreementTitle: '服务协议'
    }
  },

  created() {
    // 从路由参数获取PDF URL和标题，强制使用HTTPS
    let url = this.$route.query.url || ''
    if (url && url.startsWith('http://')) {
      url = url.replace('http://', 'https://')
    }
    this.pdfUrl = url
    this.agreementTitle = this.$route.query.title || '服务协议'
    
    if (!this.pdfUrl) {
      Toast('协议链接无效')
      this.goBack()
    }
  },

  methods: {
    goBack() {
      // 返回上一页
      this.$router.go(-1)
    },

    confirmRead() {
      // 确认已阅读，返回登录页面并标记为已同意
      this.$router.push({
        path: '/login',
        query: {
          agreed: 'true'
        }
      })
    }
  }
}
</script>

<style scoped>
.agreement-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4285f4;
  cursor: pointer;
  font-size: 16px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 60px; /* 与返回按钮宽度保持平衡 */
}

.content-container {
  flex: 1;
  overflow: hidden;
  background: #f5f5f5;
}

.footer {
  padding: 16px;
  background: #fff;
  border-top: 1px solid #eee;
}

.confirm-btn {
  border-radius: 25px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  background: #4285f4;
  border-color: #4285f4;
}

/* Vant组件样式覆盖 */
:deep(.van-button--primary) {
  background: #4285f4;
  border-color: #4285f4;
}

:deep(.van-icon) {
  font-size: 18px;
}
</style>
