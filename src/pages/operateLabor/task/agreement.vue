<template>
  <div class="agreement-page">
    <!-- 头部导航 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <van-icon name="arrow-left" />
        <span>返回</span>
      </div>
      <div class="title">{{ agreementTitle }}</div>
      <div class="placeholder"></div>
    </div>

    <!-- PDF内容展示区域 -->
    <div class="content-container">
      <!-- 优先显示iframe -->
      <iframe
        v-if="!showFallback"
        :src="pdfUrl"
        frameborder="0"
        width="100%"
        height="100%"
        style="border: none;"
        @error="onIframeError"
        @load="onIframeLoad"
      ></iframe>

      <!-- 备用方案：显示下载链接和提示 -->
      <div v-else class="fallback-content">
        <div class="fallback-message">
          <van-icon name="warning-o" size="48" color="#ff9500" />
          <h3>无法直接预览PDF文件</h3>
          <p>您的浏览器可能不支持PDF预览，请点击下方按钮下载查看</p>
          <Button
            type="primary"
            size="large"
            @click="downloadPdf"
            class="download-btn"
          >
            <van-icon name="down" />
            下载协议文件
          </Button>
          <Button
            type="default"
            size="large"
            @click="viewHtmlVersion"
            class="html-btn"
          >
            查看网页版本
          </Button>
        </div>
      </div>
    </div>

    <!-- 底部确认按钮 -->
    <div class="footer">
      <Button
        type="primary"
        size="large"
        @click="confirmRead"
        block
        class="confirm-btn"
      >
        已阅读并同意
      </Button>
    </div>
  </div>
</template>

<script>
import { Button, Icon, Toast } from 'vant'

export default {
  name: 'Agreement',

  components: {
    Button,
    [Icon.name]: Icon
  },

  data() {
    return {
      pdfUrl: '',
      agreementTitle: '服务协议',
      showFallback: false, // 是否显示备用内容
      isMobile: false // 是否为移动端
    }
  },

  created() {
    // 检测是否为移动端
    this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

    // 设置PDF文件路径
    this.pdfUrl = '/agreement.pdf'
    console.log('pdfUrl===', this.pdfUrl)
    console.log('isMobile===', this.isMobile)
    this.agreementTitle = this.$route.query.title || '服务协议'

    // 检查PDF是否可访问
    this.checkPdfAvailability()
  },

  methods: {
    goBack() {
      // 返回上一页
      this.$router.go(-1)
    },

    confirmRead() {
      // 确认已阅读，返回登录页面并标记为已同意
      this.$router.push({
        path: '/login',
        query: {
          agreed: 'true'
        }
      })
    },

    // 检查PDF文件是否可访问
    async checkPdfAvailability() {
      try {
        const response = await fetch(this.pdfUrl, { method: 'HEAD' })
        if (!response.ok) {
          console.warn('PDF文件不可访问，状态码:', response.status)
          this.handlePdfError()
        }
      } catch (error) {
        console.error('检查PDF文件时出错:', error)
        this.handlePdfError()
      }
    },

    // 处理PDF加载错误
    handlePdfError() {
      console.warn('PDF加载失败，切换到备用方案')
      this.showFallback = true
      Toast('PDF预览失败，已切换到备用方案')
    },

    // iframe加载错误处理
    onIframeError() {
      console.error('iframe加载PDF失败')
      this.handlePdfError()
    },

    // iframe加载成功处理
    onIframeLoad() {
      console.log('PDF加载成功')
    },

    // 下载PDF文件
    downloadPdf() {
      const link = document.createElement('a')
      link.href = this.pdfUrl
      link.download = 'agreement.pdf'
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    // 查看HTML版本
    viewHtmlVersion() {
      // 打开HTML版本的协议
      window.open('/agreement.vue', '_blank')
    }
  }
}
</script>

<style scoped>
.agreement-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4285f4;
  cursor: pointer;
  font-size: 16px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 60px; /* 与返回按钮宽度保持平衡 */
}

.content-container {
  flex: 1;
  overflow: hidden;
  background: #f5f5f5;
}

.fallback-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.fallback-message {
  text-align: center;
  max-width: 300px;
}

.fallback-message h3 {
  margin: 16px 0 12px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.fallback-message p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.download-btn {
  margin-bottom: 12px;
  border-radius: 25px;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  background: #4285f4;
  border-color: #4285f4;
}

.html-btn {
  border-radius: 25px;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  color: #4285f4;
  border-color: #4285f4;
}

.footer {
  padding: 16px;
  background: #fff;
  border-top: 1px solid #eee;
}

.confirm-btn {
  border-radius: 25px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  background: #4285f4;
  border-color: #4285f4;
}

/* Vant组件样式覆盖 */
:deep(.van-button--primary) {
  background: #4285f4;
  border-color: #4285f4;
}

:deep(.van-icon) {
  font-size: 18px;
}
</style>
