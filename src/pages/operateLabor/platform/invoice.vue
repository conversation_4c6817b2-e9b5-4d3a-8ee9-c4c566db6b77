<template>
  <div
    style="padding: 20px; height: 100%; overflow-y: auto"
    v-loading="loading"
  >
    <div v-if="invoiceData">
      <Title title="基本信息" />
      <el-row
        :gutter="20"
        style="
          margin-top: 20px;
          font-size: 14px;
          line-height: 32px;
          padding-left: 22px;
        "
      >
        <el-col :span="8"
          ><div>
            <label>客户：</label>{{ invoiceData.customerName }}
          </div></el-col
        >
        <el-col :span="8"
          ><div>
            <label>开票主体：</label>{{ invoiceData.supplierCorporationName }}
          </div></el-col
        >
        <el-col :span="8"
          ><div>
            <label>发票类型：</label>{{ invoiceData.typeDesc }}
          </div></el-col
        >
        <el-col :span="8"
          ><div><label>申请编号：</label>{{ invoiceData.sn }}</div></el-col
        >
        <el-col :span="8"
          ><div><label>状态：</label>{{ invoiceData.statusDesc }}</div></el-col
        >
        <el-col :span="8"
          ><div><label>开票金额：</label> {{ formatAmount(invoiceData.fee) }}</div></el-col
        >
        <el-col :span="8"
          ><div>
            <label>申请时间：</label>{{ invoiceData.createTime }}
          </div></el-col
        >
      </el-row>

      <Title title="发票信息" />
      <el-row
        :gutter="20"
        style="
          margin-top: 20px;
          font-size: 14px;
          line-height: 32px;
          padding-left: 22px;
        "
      >
        <el-col :span="8"
          ><div><label>发票抬头：</label>{{ invoiceData.title }}</div></el-col
        >
        <el-col :span="8"
          ><div>
            <label>纳税人识别号：</label>{{ invoiceData.taxNo }}
          </div></el-col
        >
        <el-col :span="8"
          ><div><label>开户行：</label>{{ invoiceData.bankName }}</div></el-col
        >
        <el-col :span="8"
          ><div>
            <label>银行账号：</label>{{ invoiceData.bankAccount }}
          </div></el-col
        >
        <el-col :span="8"
          ><div>
            <label>注册地址：</label>{{ invoiceData.registerAddress }}
          </div></el-col
        >
        <el-col :span="8"
          ><div>
            <label>企业电话：</label>{{ invoiceData.companyTel }}
          </div></el-col
        >
        <el-col :span="16"
          ><div><label>发票备注：</label>{{ invoiceData.remark }}</div></el-col
        >
      </el-row>

      <Title title="开票明细" />
      <el-table
        :data="invoiceData.items"
        style="width: 100%; margin-top: 20px; padding-left: 22px"
        size="small"
        show-summary
        :summary-method="getSummaries"
        :header-cell-style="{
          'font-size': '12px',
          'font-weight': '400',
          color: '#777c94',
          background: 'var(--o-primary-bg-color)'
        }"
      >
        <el-table-column
          prop="billNo"
          label="账单编号"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="billMonth"
          label="账单月份"
          width="150"
        ></el-table-column>
        <el-table-column
          prop="invoiceCategory"
          label="发票类目"
          width="200"
        ></el-table-column>
        <el-table-column label="开票金额">
          <template slot-scope="scope">
            {{ formatAmount(scope.row.fee) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import formatAmount from 'kit/formatters/formatAmount'

const client = makeClient()

export default {
  name: 'InvoiceDetail',
  components: {
    Title
  },
  data() {
    return {
      loading: true,
      invoiceData: null
    }
  },
  async created() {
    await this.getInvoiceDetail()
  },
  methods: {
    formatAmount,
    async getInvoiceDetail() {
      this.loading = true
      try {
        const invoiceId = this.$route.params.id
        if (!invoiceId) {
          this.$message.error('未找到发票ID')
          return
        }
        const [err, res] = await client.supplierInvoicesDetail({
          body: {
            invoiceId: parseInt(invoiceId, 10)
          }
        })
        if (err) {
          handleError(err)
          return
        }
        this.invoiceData = res.data
      } finally {
        this.loading = false
      }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((_, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        // 检查是否是开票金额列（最后一列）
        if (index === columns.length - 1) {
          const values = data.map(item => Number(item.fee))
          if (!values.every(value => isNaN(value))) {
            const total = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = formatAmount(total)
          } else {
            sums[index] = 'N/A'
          }
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>

<style scoped>
.el-row .el-col > div {
  margin-bottom: 10px;
}
.el-row .el-col > div label {
  margin-right: 8px;
  width: 120px;
  text-align: right;
}
</style>
