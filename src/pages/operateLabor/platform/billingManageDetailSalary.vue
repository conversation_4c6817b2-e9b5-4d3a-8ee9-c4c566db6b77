<template>
  <div
    class="billingManageDetailSalary"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <div class="info" style="margin-bottom: 10px">
      <span>结算项目：{{ info.feeTypeDesc }}</span>
      <span>结算月份：{{ info.billMonth }}</span>
      <span>所属服务合同：{{ info.contractName }}</span>
      <span>办理人数：{{ info.personCount }}</span>
      <span>应收金额：{{ info.totalAmount }}</span>
    </div>
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="110px"
    >
      <el-input
        v-model="conditions.filters.laborName"
        placeholder="请输入员工姓名查询"
        style="width: 280px"
        clearable
      ></el-input>
      <el-button style="margin-left: 10px" type="primary" @click="getList">查询</el-button>
    </el-form>

    <el-table
      v-loading="loading"
      size="small"
      :data="tableData"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="laborName"
        label="姓名"
        min-width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="id"
        label="工号"
        min-width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="idCard"
        label="证件号码"
        min-width="160"
      ></el-table-column>
      <el-table-column
        prop="grossSalary"
        label="本期应发金额"
        min-width="160"
      ></el-table-column>
      <el-table-column
        prop="netSalary"
        label="本月已发收入"
        min-width="160"
      ></el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import ServiceContractsSelector from './selector/serviceContracts.vue'
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    ServiceContractsSelector
  },
  data() {
    return {
      info: {},
      conditions: {
        offset: 0,
        limit: 10,
        // sorts: [
        //   {
        //     field: '',
        //     direction: ''
        //   }
        // ],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          // id: 0,
          billMasterId: 0,
          // billCategoryId: 0,
          // salaryDetailId: 0,
          // salaryBatchId: 0,
          laborName: ''
          // idCard: '',
          // billMonth: '',
          // billMonthStart: '',
          // billMonthEnd: ''
        }
      },
      tableData: [],
      total: 0,
      loading: false
    }
  },
  created() {
    this.info = {
      ...JSON.parse(this.$route.query.rowData),
      contractName: this.$route.query.contractName
    }
    this.conditions.filters.billMasterId = this.$route.query.billMasterId
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true

      const [err, r] = await client.apiSupplierBillSalaryDetails({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0
    },
  }
}
</script>
<style scoped>
.info span {
  font-size: 14px;
  margin-right: 30px;
}
</style>
