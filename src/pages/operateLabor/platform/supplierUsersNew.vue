<template>
  <div class="supplier-users-new">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="100px"
      style="width: 600px"
    >
      <Title title="基本信息" />
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="cellphone">
        <el-input
          v-model="form.cellphone"
          placeholder="请输入手机号"
        ></el-input>
      </el-form-item>
      <el-form-item label="角色名称" prop="roleIds">
        <RolesSelector v-model="form.roleIds" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">{{
          isEdit ? '保存' : '创建'
        }}</el-button>
        <el-button @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from 'kit/services/operateLabor/makeClient'
import Title from './components/title.vue'
import RolesSelector from './selector/roles.vue'

const client = makeClient()

export default {
  components: { Title, RolesSelector },
  computed: {
    isEdit() {
      return !!this.$route.params.id
    }
  },
  data() {
    return {
      form: {
        name: '',
        cellphone: '',
        roleIds: []
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        cellphone: [
          { required: true, message: '请输入手机号', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    if (this.isEdit) {
      const { name, cellphone, roleIds } = this.$route.query
      this.form.name = name
      this.form.cellphone = cellphone
      if (roleIds) {
        this.form.roleIds = roleIds.split(',').map(id => id * 1)
      }
    }
  },
  methods: {
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      if (this.isEdit) {
        const payload = {
          ...this.form,
          merberId: this.$route.params.id
        }
        const [err] = await client.supplierEditMember({ body: payload })
        if (err) {
          handleError(err)
          return
        }
        this.$message.success('更新成功')
      } else {
        const [err] = await client.supplierAddMember({ body: this.form })
        if (err) {
          handleError(err)
          return
        }
        this.$message.success('创建成功')
      }

      this.$router.push('/supplierUsers')
    }
  }
}
</script>
