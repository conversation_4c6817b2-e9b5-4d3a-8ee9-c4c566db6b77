import store from 'kit/helpers/store'

const setSupplierUserProfile = profile => {
  store.set('supplierUserProfile', profile)
}

const getSupplierUserProfile = () => {
  const supplierUserProfile = store.get('supplierUserProfile')
  return JSON.parse(supplierUserProfile)
}

// 清理所有与域名相关的本地存储数据
const clearDomainRelatedData = () => {
  localStorage.removeItem('domainInfo')
  localStorage.removeItem('supplierUserProfile')
  localStorage.removeItem('smsCountdownEndTime')
  localStorage.removeItem('__authorities__')
}

// 检查当前域名是否与存储的域名信息匹配
const isDomainInfoValid = () => {
  const domainInfo = localStorage.getItem('domainInfo')
  if (!domainInfo) {
    return false
  }

  try {
    const parsedDomainInfo = JSON.parse(domainInfo)
    let currentDomain = window.location.host

    if (currentDomain.includes('localhost')) {
      currentDomain = '156-dev.olading.com'
    }

    return parsedDomainInfo.domainName === currentDomain
  } catch (error) {
    console.error('Failed to parse domainInfo:', error)
    return false
  }
}

export {
  setSupplierUserProfile,
  getSupplierUserProfile,
  clearDomainRelatedData,
  isDomainInfoValid
}
